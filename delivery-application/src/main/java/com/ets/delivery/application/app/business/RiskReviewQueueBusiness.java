package com.ets.delivery.application.app.business;

import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.common.consts.ReviewQueueCacheKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控审核队列业务类
 */
@Component
public class RiskReviewQueueBusiness {

    @Resource(name = "defaultRedisTemplate")
    StringRedisTemplate redisTemplate;

    /**
     * 获取用户风控审核列表（按类型）
     * @param username 用户名
     * @param riskType 风控类型
     * @return 风控审核单号列表
     */
    public List<String> getUserRiskReviewList(String username, Integer riskType) {
        List<String> list = new ArrayList<>();
        String userRiskReviewSnStr = redisTemplate.opsForValue().get(ReviewQueueCacheKey.getUserRiskReviewKey(username, riskType));
        if (StringUtils.isNotEmpty(userRiskReviewSnStr)) {
            list = JSON.parseArray(userRiskReviewSnStr, String.class);
        }
        return list;
    }

    /**
     * 添加用户风控审核（按类型）
     * @param username 用户名
     * @param riskReviewSn 风控审核单号
     * @param riskType 风控类型
     */
    public void addUserRiskReview(String username, String riskReviewSn, Integer riskType) {
        List<String> list = new ArrayList<>();
        String key = ReviewQueueCacheKey.getUserRiskReviewKey(username, riskType);
        String userRiskReviewSnStr = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(userRiskReviewSnStr)) {
            list = JSON.parseArray(userRiskReviewSnStr, String.class);
        }
        if (!list.contains(riskReviewSn)) {
            list.add(riskReviewSn);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(list));
        }
    }

    /**
     * 移除用户风控审核（按类型）
     * @param username 用户名
     * @param riskReviewSn 风控审核单号
     * @param riskType 风控类型
     */
    public void removeUserRiskReview(String username, String riskReviewSn, Integer riskType) {
        String key = ReviewQueueCacheKey.getUserRiskReviewKey(username, riskType);
        String userRiskReviewSnStr = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(userRiskReviewSnStr)) {
            List<String> list = JSON.parseArray(userRiskReviewSnStr, String.class);
            list.remove(riskReviewSn);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(list));
        }
    }

    /**
     * 推送到风控审核队列
     * @param riskReviewSn 风控审核单号
     * @param riskType 风控类型
     */
    public void pushToRiskReviewQueueList(String riskReviewSn, Integer riskType) {
        String key = ReviewQueueCacheKey.getRiskReviewQueueKey(riskType);
        redisTemplate.opsForList().leftPush(key, riskReviewSn);
    }

    /**
     * 推送到风控审核队列（兼容旧方法，默认初审）
     * @param riskReviewSn 风控审核单号
     */
    public void pushToRiskReviewQueueList(String riskReviewSn) {
        pushToRiskReviewQueueList(riskReviewSn, 1); // 默认初审
    }

    /**
     * 重新推送到风控审核队列
     * @param riskReviewSn 风控审核单号
     * @param riskType 风控类型
     */
    public void rePushToRiskReviewQueueList(String riskReviewSn, Integer riskType) {
        String key = ReviewQueueCacheKey.getRiskReviewQueueKey(riskType);
        redisTemplate.opsForList().rightPush(key, riskReviewSn);
    }

    /**
     * 从风控审核队列弹出一条数据
     * @param riskType 风控类型 1-初审 2-复审
     * @return 风控审核单号
     */
    public String popFromRiskReviewQueueList(Integer riskType) {
        String key = ReviewQueueCacheKey.getRiskReviewQueueKey(riskType);
        // 默认倒序（最新的先获取）
        return redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 获取风控审核队列列表
     * @param riskType 风控类型
     * @param start 开始位置
     * @param end 结束位置
     * @return 风控审核单号列表
     */
    public List<String> getRiskReviewQueueList(Integer riskType, long start, long end) {
        String key = ReviewQueueCacheKey.getRiskReviewQueueKey(riskType);
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 删除风控审核队列
     * @param riskType 风控类型
     */
    public void delRiskReviewQueueList(Integer riskType) {
        String key = ReviewQueueCacheKey.getRiskReviewQueueKey(riskType);
        redisTemplate.delete(key);
    }
}
