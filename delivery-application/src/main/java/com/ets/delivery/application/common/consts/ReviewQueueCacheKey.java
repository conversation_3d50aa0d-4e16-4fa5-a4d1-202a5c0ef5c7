package com.ets.delivery.application.common.consts;

public class ReviewQueueCacheKey {

    // 审核数据缓存时长
    public static final long REVIEW_DATA_CACHE_TIME = 7 * 24 * 3600;

    // 后审队列key
    public static final String POST_REVIEW_QUEUE_KEY = "Car:Review:PostReview";


    // 用户后审key
    public static String getUserPostReviewKey(String username) {
        return "Car:Review:User:" + username + ":PostReview";
    }

    // 用户风控审核key
    public static String getUserRiskReviewKey(String username) {
        return "Review:User:" + username + ":RiskReview";
    }

    // 审核数据key
    public static String getReviewDataKey(String reviewSn) {
        return "Car:Review:Data:" + reviewSn;
    }

    // 后审队列区分优先级key
    public static String getPostReviewQueueKey(Integer emergencyType) {
        return "Car:Review:PostReview:" + emergencyType;
    }

    // 风控审核队列key（区分初审和复审）
    public static String getRiskReviewQueueKey(Integer riskType) {
        if (riskType != null) {
            return "Review:RiskReview:" + riskType;
        }
        return "Review:RiskReview";
    }

    // 风控初审队列key
    public static String getRiskPreviewQueueKey() {
        return "Review:RiskReview:1"; // 1-初审
    }

    // 风控复审队列key
    public static String getRiskRecheckQueueKey() {
        return "Review:RiskReview:2"; // 2-复审
    }
}
