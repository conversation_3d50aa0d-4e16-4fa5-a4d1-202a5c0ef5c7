# RiskReview队列功能实现说明

## 概述
参考PostReviewQueue的实现，为RiskReview实现了队列功能。与PostReviewQueue不同的是：
- 不需要缓存数据
- 不需要做成批量获取，每次只获取一条数据
- 区分初审和复审，使用不同队列
- 使用现有的 `/get-preview` 和 `/get-recheck` 接口

## 实现的文件

### 1. 新增文件



#### RiskReviewQueueBusiness
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/app/business/RiskReviewQueueBusiness.java`
- 功能：风控审核队列业务逻辑类
- 主要方法：
  - `getUserRiskReviewList()` - 获取用户风控审核列表
  - `addUserRiskReview()` - 添加用户风控审核
  - `removeUserRiskReview()` - 移除用户风控审核
  - `pushToRiskReviewQueueList(String, Integer)` - 推送到风控审核队列（按类型）
  - `rePushToRiskReviewQueueList(String, Integer)` - 重新推送到风控审核队列（按类型）
  - `popFromRiskReviewQueueList(Integer)` - 从风控审核队列弹出一条数据（按类型）
  - `getRiskReviewQueueList(Integer, long, long)` - 获取风控审核队列列表（按类型）
  - `delRiskReviewQueueList(Integer)` - 删除风控审核队列（按类型）

### 2. 修改的文件

#### ReviewQueueCacheKey
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/common/consts/ReviewQueueCacheKey.java`
- 新增：
  - `RISK_REVIEW_QUEUE_KEY` - 风控审核队列key常量
  - `getUserRiskReviewKey()` - 用户风控审核key生成方法
  - `getRiskReviewQueueKey(Integer riskType)` - 风控审核队列key生成方法（区分类型）
  - `getRiskPreviewQueueKey()` - 风控初审队列key
  - `getRiskRecheckQueueKey()` - 风控复审队列key

#### RiskReviewBusiness
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/app/business/RiskReviewBusiness.java`
- 新增依赖：`RiskReviewQueueBusiness`
- 新增方法：
  - `receive()` - 获取风控初审数据（每次只获取一条）
  - `receiveRecheck()` - 获取风控复审数据（每次只获取一条）
  - `getOneRiskReviewByType(Integer)` - 从队列获取指定类型的风控审核单（核心统一方法）
  - `removeUserRiskReviewRecord(String)` - 移除用户风控审核领取记录
  - `getCurrentUserRiskReviewList()` - 获取当前用户的风控审核领取列表
- 修改方法：
  - `createRiskReview()` - 创建风控审核单时推送到队列
  - `riskInfoUpload()` - 复审时推送到队列

#### AdminRiskReviewsController
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/controller/admin/AdminRiskReviewsController.java`
- 修改API：
  - `/admin/risk-reviews/get-preview` - 获取风控初审数据
  - `/admin/risk-reviews/get-recheck` - 获取风控复审数据
- 新增API：
  - `/admin/risk-reviews/get-user-review-list` - 获取当前用户的风控审核领取列表
  - `/admin/risk-reviews/remove-user-review-record` - 移除用户风控审核领取记录

#### RiskReviewsService
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/infra/service/RiskReviewsService.java`
- 新增方法：`getDefaultListLimitDate()` - 获取待审核的风控审核单列表（用于队列重新入队）

## 队列工作流程

### 1. 创建风控审核单
1. 调用 `RiskReviewBusiness.createRiskReview()`
2. 保存风控审核单到数据库
3. 调用 `riskReviewQueueBusiness.pushToRiskReviewQueueList()` 推送到队列

### 2. 复审流程
1. 调用 `RiskReviewBusiness.riskInfoUpload()`
2. 创建复审记录
3. 调用 `riskReviewQueueBusiness.pushToRiskReviewQueueList()` 推送到队列

### 3. 获取风控初审数据
1. 调用 `/admin/risk-reviews/get-preview` API
2. 直接调用 `getOneRiskReviewByType(1)` 获取初审数据
3. 从初审队列中弹出一条记录：`riskReviewQueueBusiness.popFromRiskReviewQueueList(1)`
4. 如果队列为空，从数据库查询待审核的初审记录并重新入队
5. 添加用户领取记录到Redis：`riskReviewQueueBusiness.addUserRiskReview(username, riskReviewSn)`
6. 返回风控审核详情

### 4. 获取风控复审数据
1. 调用 `/admin/risk-reviews/get-recheck` API
2. 直接调用 `getOneRiskReviewByType(2)` 获取复审数据
3. 从复审队列中弹出一条记录：`riskReviewQueueBusiness.popFromRiskReviewQueueList(2)`
4. 如果队列为空，从数据库查询待审核的复审记录并重新入队
5. 添加用户领取记录到Redis：`riskReviewQueueBusiness.addUserRiskReview(username, riskReviewSn)`
6. 返回风控审核详情

### 5. 用户领取记录管理
1. **查看领取列表**：调用 `/admin/risk-reviews/get-user-review-list` API 获取当前用户已领取的风控审核单列表
2. **移除领取记录**：调用 `/admin/risk-reviews/remove-user-review-record` API 移除指定的用户领取记录
3. **自动记录**：用户获取风控审核单时自动添加领取记录

## Redis缓存Key设计

- 风控初审队列：`Review:RiskReview:1`
- 风控复审队列：`Review:RiskReview:2`
- 用户风控审核：`Review:User:{username}:RiskReview`

## API接口

### 获取风控初审数据
- **URL**: `POST /admin/risk-reviews/get-preview`
- **请求参数**: 无
- **响应**: 风控审核详情VO

### 获取风控复审数据
- **URL**: `POST /admin/risk-reviews/get-recheck`
- **请求参数**: 无
- **响应**: 风控审核详情VO

### 获取当前用户的风控审核领取列表
- **URL**: `POST /admin/risk-reviews/get-user-review-list`
- **请求参数**: 无
- **响应**: 风控审核单号列表

### 移除用户风控审核领取记录
- **URL**: `POST /admin/risk-reviews/remove-user-review-record`
- **请求参数**: `riskReviewSn` (String) - 风控审核单号
- **响应**: 操作结果

## 与PostReviewQueue的主要差异

1. **不缓存数据**：RiskReview不需要像PostReview一样缓存审核数据
2. **单条获取**：每次只获取一条数据，不支持批量获取
3. **分类队列**：区分初审和复审，使用不同的队列
4. **使用现有接口**：复用 `/get-preview` 和 `/get-recheck` 接口
5. **无需参数**：接口不需要请求参数，默认倒序获取
6. **统一方法**：合并队列操作方法，用参数区分初审复审
7. **用户领取记录**：自动记录用户领取的风控审核单，支持查看和移除
8. **简化逻辑**：去除了排序选择等复杂逻辑，保留必要的用户管理功能

## 使用说明

1. 创建风控审核单时会自动推送到初审队列
2. 复审时会自动推送新的复审单到复审队列
3. 通过 `/admin/risk-reviews/get-preview` API 可以获取初审队列中的风控审核单
4. 通过 `/admin/risk-reviews/get-recheck` API 可以获取复审队列中的风控审核单
5. 默认倒序获取（最新的先获取）
6. 队列为空时会自动从数据库补充对应类型的待审核记录
7. 用户领取风控审核单时会自动记录到个人领取列表
8. 支持查看个人领取列表和移除领取记录
