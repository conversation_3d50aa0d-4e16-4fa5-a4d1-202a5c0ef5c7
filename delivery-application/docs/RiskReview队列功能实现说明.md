# RiskReview队列功能实现说明

## 概述
参考PostReviewQueue的实现，为RiskReview实现了队列功能。与PostReviewQueue不同的是：
- 不需要缓存数据
- 不需要做成批量获取，每次只获取一条数据
- 不区分紧急类型，使用统一队列

## 实现的文件

### 1. 新增文件

#### RiskReviewBatchReceiveDTO
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/common/dto/riskreview/RiskReviewBatchReceiveDTO.java`
- 功能：风控审核批量接收DTO，包含排序参数

#### RiskReviewQueueBusiness
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/app/business/RiskReviewQueueBusiness.java`
- 功能：风控审核队列业务逻辑类
- 主要方法：
  - `getUserRiskReviewList()` - 获取用户风控审核列表
  - `addUserRiskReview()` - 添加用户风控审核
  - `removeUserRiskReview()` - 移除用户风控审核
  - `pushToRiskReviewQueueList()` - 推送到风控审核队列
  - `rePushToRiskReviewQueueList()` - 重新推送到风控审核队列
  - `popFromRiskReviewQueueList()` - 从风控审核队列弹出一条数据
  - `getRiskReviewQueueList()` - 获取风控审核队列列表
  - `delRiskReviewQueueList()` - 删除风控审核队列

### 2. 修改的文件

#### ReviewQueueCacheKey
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/common/consts/ReviewQueueCacheKey.java`
- 新增：
  - `RISK_REVIEW_QUEUE_KEY` - 风控审核队列key常量
  - `getUserRiskReviewKey()` - 用户风控审核key生成方法
  - `getRiskReviewQueueKey()` - 风控审核队列key生成方法

#### RiskReviewBusiness
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/app/business/RiskReviewBusiness.java`
- 新增依赖：`RiskReviewQueueBusiness`
- 新增方法：
  - `receive()` - 接收风控审核单（每次只获取一条）
  - `getOneRiskReview()` - 从队列获取风控审核单
- 修改方法：
  - `createRiskReview()` - 创建风控审核单时推送到队列
  - `riskInfoUpload()` - 复审时推送到队列

#### AdminRiskReviewsController
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/controller/admin/AdminRiskReviewsController.java`
- 新增API：`/admin/risk-reviews/receive` - 接收风控审核单

#### RiskReviewsService
- 路径：`delivery-application/src/main/java/com/ets/delivery/application/infra/service/RiskReviewsService.java`
- 新增方法：`getDefaultListLimitDate()` - 获取待审核的风控审核单列表（用于队列重新入队）

## 队列工作流程

### 1. 创建风控审核单
1. 调用 `RiskReviewBusiness.createRiskReview()`
2. 保存风控审核单到数据库
3. 调用 `riskReviewQueueBusiness.pushToRiskReviewQueueList()` 推送到队列

### 2. 复审流程
1. 调用 `RiskReviewBusiness.riskInfoUpload()`
2. 创建复审记录
3. 调用 `riskReviewQueueBusiness.pushToRiskReviewQueueList()` 推送到队列

### 3. 接收风控审核单
1. 调用 `/admin/risk-reviews/receive` API
2. 从队列中弹出一条记录：`riskReviewQueueBusiness.popFromRiskReviewQueueList()`
3. 如果队列为空，从数据库查询待审核记录并重新入队
4. 返回风控审核详情

## Redis缓存Key设计

- 风控审核队列：`Car:Review:RiskReview`
- 用户风控审核：`Car:Review:User:{username}:RiskReview`

## API接口

### 接收风控审核单
- **URL**: `POST /admin/risk-reviews/receive`
- **请求参数**: 
  ```json
  {
    "sort": "asc"  // 排序方式：asc-正序，desc-倒序
  }
  ```
- **响应**: 风控审核详情VO

## 与PostReviewQueue的主要差异

1. **不缓存数据**：RiskReview不需要像PostReview一样缓存审核数据
2. **单条获取**：每次只获取一条数据，不支持批量获取
3. **统一队列**：不区分紧急类型，使用统一的队列
4. **简化逻辑**：去除了用户领取记录管理等复杂逻辑

## 使用说明

1. 创建风控审核单时会自动推送到队列
2. 复审时会自动推送新的复审单到队列
3. 通过 `/admin/risk-reviews/receive` API 可以获取队列中的风控审核单
4. 支持正序和倒序获取
5. 队列为空时会自动从数据库补充待审核记录
